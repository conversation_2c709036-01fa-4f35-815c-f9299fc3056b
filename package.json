{"private": true, "type": "module", "scripts": {"dev": "vite", "watch": "vite build --watch", "build": "vite build"}, "devDependencies": {"@inertiajs/vue3": "^1.0.0", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-vue": "^2.3.4", "autoprefixer": "^10.4.17", "axios": "^1.1.2", "laravel-vite-plugin": "^0.7.8", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "vite": "^2.9.18", "vue": "^3.2.41"}, "dependencies": {"@inertiajs/inertia": "^0.11.1", "@vueup/vue-quill": "^1.2.0", "@yaireo/tagify": "^4.33.2", "chart.js": "^4.4.3", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "laravel-precognition-vue-inertia": "^0.5.4", "vue-chartjs": "^5.3.1"}}