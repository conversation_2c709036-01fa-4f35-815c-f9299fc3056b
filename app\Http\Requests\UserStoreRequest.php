<?php

namespace App\Http\Requests;

use App\DTO\UserDTO;
use Support\Contracts\HasDTO;
use Illuminate\Foundation\Http\FormRequest;
use App\Models\User;
use Illuminate\Validation\Rules;

class UserStoreRequest extends FormRequest implements HasDTO
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'first_name'=> ['required','string','max:255'],
            'last_name' => ['required','string','max:255'],
            'email'     => ['required','string','email','max:255','unique:'.User::class],
            'password'  => ['required', Rules\Password::defaults()],
            'role_id'   => ['required'],
            // 'contact_no'=> ['required','numeric', 'digits:10'],
            'dob'       => ['required'],
            // 'address'   => ['required'],
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'The first name field is required.',
            'first_name.string' => 'The first name must be a string.',
            'first_name.max' => 'The first name may not be greater than 255 characters.',
            'last_name.required' => 'The last name field is required.',
            'last_name.string' => 'The last name must be a string.',
            'last_name.max' => 'The last name may not be greater than 255 characters.',
            'email.required' => 'The email field is required.',
            'email.string' => 'The email must be a string.',
            'email.email' => 'The email must be a valid email address.',
            'email.max' => 'The email may not be greater than 255 characters.',
            'email.unique' => 'The email has already been taken.',
            'password.required' => 'The password field is required.',
            'role_id.required' => 'The role field is required.',
            'dob.required' => 'The date of birth field is required.',
        ];
    }

    /**
     * Get custom attribute names.
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'first_name' => 'first name',
            'last_name' => 'last name',
            'email' => 'email address',
            'password' => 'password',
            'role_id' => 'role',
            'contact_no' => 'contact number',
            'dob' => 'date of birth',
            'address' => 'address',
        ];
    }

    public function DTO()
    {
        return UserDTO::LazyFromArray($this->input());
    }

}
